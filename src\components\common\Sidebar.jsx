import React, { useState, useEffect } from "react";
import {
  ChevronDown,
  ChevronRight,
  FolderOpen,
  Network,
  Plus,
  Settings,
  Menu,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export function Sidebar({
  isOpen,
  onToggle,
  className,
  onCreateProject,
  refreshTrigger,
}) {
  const [isProjectsExpanded, setIsProjectsExpanded] = useState(true);
  const [projects, setProjects] = useState([]);
  const [selectedProject, setSelectedProject] = useState(null);

  // Load projects từ database
  useEffect(() => {
    loadProjects();
  }, [refreshTrigger]);

  const loadProjects = async () => {
    try {
      // Gọi IPC để lấy danh sách projects từ main process
      const projectList = await window.electronAPI.getProjects();
      setProjects(projectList);
    } catch (error) {
      console.error("Failed to load projects:", error);
    }
  };

  const handleCreateProject = () => {
    // Emit event để mở dialog tạo project
    if (onCreateProject) {
      onCreateProject();
    } else if (
      window.electronAPI &&
      window.electronAPI.openCreateProjectDialog
    ) {
      window.electronAPI.openCreateProjectDialog();
    } else {
      // Fallback: dispatch custom event
      window.dispatchEvent(new CustomEvent("open-create-project-dialog"));
    }
  };

  const handleProjectSelect = (project) => {
    setSelectedProject(project);
    // Emit event để thông báo project được chọn
    window.electronAPI.selectProject(project);
  };

  const handleScanNetwork = () => {
    // Emit event để bắt đầu scan network
    window.electronAPI.startNetworkScan();
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed left-0 top-0 h-full bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 z-50 transition-transform duration-300 ease-in-out",
          "w-64 lg:w-72",
          isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
          className
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            Engine Toolkit
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="lg:hidden"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {/* Projects Section */}
          <div>
            <Button
              variant="ghost"
              className="w-full justify-between p-2 h-auto"
              onClick={() => setIsProjectsExpanded(!isProjectsExpanded)}
            >
              <div className="flex items-center space-x-2">
                <FolderOpen className="h-4 w-4" />
                <span>Projects</span>
              </div>
              {isProjectsExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>

            {/* Projects List */}
            {isProjectsExpanded && (
              <div className="ml-4 mt-2 space-y-1">
                {projects.map((project) => (
                  <Button
                    key={project.id}
                    variant={
                      selectedProject?.id === project.id ? "secondary" : "ghost"
                    }
                    className="w-full justify-start p-2 h-auto text-sm"
                    onClick={() => handleProjectSelect(project)}
                  >
                    <div className="flex flex-col items-start">
                      <span className="font-medium">{project.name}</span>
                      {project.description && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {project.description}
                        </span>
                      )}
                    </div>
                  </Button>
                ))}

                {/* Add Project Button */}
                <Button
                  variant="ghost"
                  className="w-full justify-start p-2 h-auto text-sm text-blue-600 dark:text-blue-400"
                  onClick={handleCreateProject}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Project
                </Button>
              </div>
            )}
          </div>

          {/* Scan Network Section */}
          <Button
            variant="ghost"
            className="w-full justify-start p-2 h-auto"
            onClick={handleScanNetwork}
          >
            <Network className="h-4 w-4 mr-2" />
            <span>Scan Network</span>
          </Button>

          {/* Settings Section */}
          <Button variant="ghost" className="w-full justify-start p-2 h-auto">
            <Settings className="h-4 w-4 mr-2" />
            <span>Settings</span>
          </Button>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Engine Toolkit v1.0.0
          </div>
        </div>
      </div>
    </>
  );
}

// Mobile toggle button
export function SidebarToggle({ onClick, className }) {
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={onClick}
      className={cn("lg:hidden", className)}
    >
      <Menu className="h-4 w-4" />
    </Button>
  );
}
