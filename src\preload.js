import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON><PERSON>er without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // Project management
    getProjects: () => ipcRenderer.invoke('get-projects'),
    createProject: (projectData) => ipc<PERSON>enderer.invoke('create-project', projectData),
    updateProject: (id, projectData) => ipcRenderer.invoke('update-project', id, projectData),
    deleteProject: (id) => ipc<PERSON>enderer.invoke('delete-project', id),
    selectProjectPath: () => ipc<PERSON>enderer.invoke('select-project-path'),

    // Project selection events
    selectProject: (project) => ipcRenderer.send('project-selected', project),

    // Network scanning
    startNetworkScan: () => ipcRenderer.send('start-network-scan'),

    // Dialog helpers
    showCreateProjectDialog: () => ipcRenderer.invoke('show-create-project-dialog'),
});

// See the Electron documentation for details on how to use preload scripts:
// https://www.electronjs.org/docs/latest/tutorial/process-model#preload-scripts
