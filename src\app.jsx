import React, { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/sonner";
import { Sidebar, SidebarToggle } from "@/components/common/Sidebar";
import { CreateProjectDialog } from "@/components/common/CreateProjectDialog";

export function App() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isCreateProjectOpen, setIsCreateProjectOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Listen for create project dialog events
  useEffect(() => {
    const handleOpenCreateProjectDialog = () => {
      setIsCreateProjectOpen(true);
    };

    window.addEventListener('open-create-project-dialog', handleOpenCreateProjectDialog);

    return () => {
      window.removeEventListener('open-create-project-dialog', handleOpenCreateProjectDialog);
    };
  }, []);

  const handleCreateProject = async (projectData) => {
    try {
      await window.electronAPI.createProject(projectData);
      // Trigger refresh của sidebar
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error('Failed to create project:', error);
      throw error;
    }
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <Sidebar
        isOpen={isSidebarOpen}
        onToggle={toggleSidebar}
        onCreateProject={() => setIsCreateProjectOpen(true)}
        refreshTrigger={refreshTrigger}
        className="lg:relative lg:translate-x-0"
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <SidebarToggle onClick={toggleSidebar} />
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
                Engine Toolkit
              </h1>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 overflow-auto p-6">
          <div className="max-w-7xl mx-auto">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Welcome to Engine Toolkit
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Select a project from the sidebar to get started, or create a new project.
              </p>
            </div>
          </div>
        </main>
      </div>

      {/* Dialogs */}
      <CreateProjectDialog
        open={isCreateProjectOpen}
        onOpenChange={setIsCreateProjectOpen}
        onCreateProject={handleCreateProject}
      />

      <Toaster />
    </div>
  );
}
