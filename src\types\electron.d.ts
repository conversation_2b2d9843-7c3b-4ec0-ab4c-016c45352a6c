export interface ElectronAPI {
  // Project management
  getProjects: () => Promise<Project[]>;
  createProject: (projectData: Omit<Project, 'id' | 'created_at' | 'updated_at'>) => Promise<Project>;
  updateProject: (id: number, projectData: Omit<Project, 'id' | 'created_at' | 'updated_at'>) => Promise<Project>;
  deleteProject: (id: number) => Promise<void>;
  selectProjectPath: () => Promise<string | null>;
  
  // Project selection events
  selectProject: (project: Project) => void;
  
  // Network scanning
  startNetworkScan: () => void;
  
  // Dialog helpers
  showCreateProjectDialog?: () => Promise<boolean>;
  openCreateProjectDialog?: () => void;
}

export interface Project {
  id: number;
  name: string;
  description?: string;
  path?: string;
  created_at: string;
  updated_at: string;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
