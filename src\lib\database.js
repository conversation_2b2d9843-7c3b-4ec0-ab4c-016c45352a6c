import fs from 'node:fs';
import path from 'node:path';
import { app } from 'electron';

let dbPath = null;
let projects = [];

export function initDatabase() {
  try {
    // Tạo đường dẫn đến file database trong thư mục userData
    dbPath = path.join(app.getPath('userData'), 'projects.json');

    // Đọc dữ liệu từ file nếu tồn tại
    if (fs.existsSync(dbPath)) {
      const data = fs.readFileSync(dbPath, 'utf8');
      projects = JSON.parse(data);
    } else {
      projects = [];
      saveToFile();
    }

    console.log('Database initialized successfully');
    return true;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    projects = [];
    return false;
  }
}

function saveToFile() {
  if (dbPath) {
    try {
      fs.writeFileSync(dbPath, JSON.stringify(projects, null, 2));
    } catch (error) {
      console.error('Failed to save to file:', error);
    }
  }
}

function getNextId() {
  return projects.length > 0 ? Math.max(...projects.map(p => p.id)) + 1 : 1;
}

// Project CRUD operations
export const projectsDB = {
  // Lấy tất cả projects
  getAll() {
    return [...projects].sort((a, b) => new Date(b.updated_at) - new Date(a.updated_at));
  },

  // Lấy project theo ID
  getById(id) {
    return projects.find(p => p.id === id);
  },

  // Tạo project mới
  create(project) {
    const now = new Date().toISOString();
    const newProject = {
      id: getNextId(),
      name: project.name,
      description: project.description || '',
      path: project.path || '',
      created_at: now,
      updated_at: now
    };

    projects.push(newProject);
    saveToFile();
    return newProject;
  },

  // Cập nhật project
  update(id, project) {
    const index = projects.findIndex(p => p.id === id);
    if (index !== -1) {
      projects[index] = {
        ...projects[index],
        name: project.name,
        description: project.description || '',
        path: project.path || '',
        updated_at: new Date().toISOString()
      };
      saveToFile();
      return projects[index];
    }
    return null;
  },

  // Xóa project
  delete(id) {
    const index = projects.findIndex(p => p.id === id);
    if (index !== -1) {
      projects.splice(index, 1);
      saveToFile();
      return true;
    }
    return false;
  }
};

export function closeDatabase() {
  // Lưu dữ liệu cuối cùng trước khi đóng
  saveToFile();
}
