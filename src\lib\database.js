import Database from 'better-sqlite3';
import path from 'node:path';
import { app } from 'electron';

let db = null;

export function initDatabase() {
  try {
    // Tạo đường dẫn đến file database trong thư mục userData
    const dbPath = path.join(app.getPath('userData'), 'projects.db');

    // Khởi tạo database
    db = new Database(dbPath);

    // Tạo bảng projects nếu chưa tồn tại
    db.exec(`
      CREATE TABLE IF NOT EXISTS projects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        path TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('Database initialized successfully');
    return db;
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
}

export function getDatabase() {
  if (!db) {
    throw new Error('Database not initialized. Call initDatabase() first.');
  }
  return db;
}

// Project CRUD operations
export const projectsDB = {
  // <PERSON><PERSON><PERSON> tất cả projects
  getAll() {
    const db = getDatabase();
    return db.prepare('SELECT * FROM projects ORDER BY updated_at DESC').all();
  },

  // Lấy project theo ID
  getById(id) {
    const db = getDatabase();
    return db.prepare('SELECT * FROM projects WHERE id = ?').get(id);
  },

  // Tạo project mới
  create(project) {
    const db = getDatabase();
    const stmt = db.prepare(`
      INSERT INTO projects (name, description, path)
      VALUES (?, ?, ?)
    `);
    const result = stmt.run(project.name, project.description, project.path);
    return { id: result.lastInsertRowid, ...project };
  },

  // Cập nhật project
  update(id, project) {
    const db = getDatabase();
    const stmt = db.prepare(`
      UPDATE projects
      SET name = ?, description = ?, path = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    stmt.run(project.name, project.description, project.path, id);
    return this.getById(id);
  },

  // Xóa project
  delete(id) {
    const db = getDatabase();
    const stmt = db.prepare('DELETE FROM projects WHERE id = ?');
    return stmt.run(id);
  }
};

export function closeDatabase() {
  if (db) {
    db.close();
    db = null;
  }
}
